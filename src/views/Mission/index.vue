<template>
  <div class="add-container">
    <van-sticky>
      <van-nav-bar
          :title="tittle"
          left-text="返回"
          left-arrow
          @click-left="onClickLeft"
      />

    </van-sticky>

    <!--检查分类 start-->
    <div v-if="isCanModify">
      <van-popup
          v-model:show="isShowType"
          position="bottom"
          closeable
          :style="{ height: '40%' }"
          @close="isShowType = false"
      >
        <van-nav-bar
            title="选择检查分类"
        />
        <ul class="type-list">
          <li v-for="(item,index) in typeData" :key="item.AedtGuid" @click="selectType(item)">
            <span class="selected-span">{{item.AedtName}}</span>
          </li>
        </ul>
      </van-popup>

    </div>
    <!--检查分类 end-->

    <!--检查构件 start-->
    <van-popup
        class="hide-overflow"
        v-model:show="isShowElements"
        position="bottom"
        lock-scroll
        :style="{ height: '90%' }"
    >
      <van-sticky>
        <div class="header-container">
          <img
              width="25"
              height="25"
              :src="require('../../assets/images/common/back-arrow.png')"
              @click="closeElementsPop('close')"
              alt=""/>
          <span>构件选择</span>
          <span @click="closeElementsPop('confirm')">确认</span>
        </div>
      </van-sticky>
      <van-checkbox-group @change="eleChangeCheck" v-model="eleChecked" class="mt-10" style="height:100%;overflow-y:scroll">
        <van-cell-group>
          <van-cell
              v-for="(item, index) in elementData"
              clickable
              :key="item.bmc_guid"
              :title="isAllElement ? item.bmc_name : item.Name"
              @click="toggleElement(index,item)"
          >
            <template #icon>
              <div v-if="isAllElement || item.DirectChildrenCount > 0">
                <img
                    class="left-img"
                    width="25"
                    height="25"
                    :src="require('../../assets/images/quality/element-folder.png')" alt="">
              </div>
              <div v-else>
                <img
                    class="left-img"
                    width="25"
                    height="25"
                    :src="require('../../assets/images/quality/element-item.png')" alt="">
              </div>

            </template>
            <template #right-icon>
              <div v-if="isAllElement">
                <img
                    v-if="item.ChildrenItemCount !== 0"
                    class="right-img"
                    width="15"
                    height="15"
                    :src="require('../../assets/images/common/right-arrow.png')" alt="">
              </div>
              <div v-else>
                <van-checkbox
                    :name="item"
                    :ref="el => checkboxElementRefs[index] = el"
                    @click.stop
                />
              </div>
            </template>
          </van-cell>
        </van-cell-group>
      </van-checkbox-group>

    </van-popup>
    <!--检查构件 end-->

    <!--检查任务 start-->
    <van-popup
        class="hide-overflow"
        v-model:show="isShowTasks"
        position="bottom"
        lock-scroll
        :style="{ height: '90%' }"
    >
      <van-sticky>
        <div class="header-container">
          <img
              width="25"
              height="25"
              :src="require('../../assets/images/common/back-arrow.png')"
              @click="closeTaskPop('close')"
              alt=""/>
          <span>任务选择</span>
          <span @click="closeTaskPop('confirm')">确认</span>
        </div>
      </van-sticky>
      <van-checkbox-group @change="taskChangeCheck" v-model="taskChecked" class="mt-10" style="height:100%;overflow-y:scroll">
        <van-cell-group>
          <van-cell
              v-for="(item, index) in planData"
              clickable
              :key="index"
              :title="item.NAME_"
              @click="toggleTask(index,item)"
          >
            <template #icon>
              <img
                  class="left-img"
                  v-if="item.bop_planId"
                  width="25"
                  height="25"
                  :src="require('../../assets/images/quality/element-folder.png')" alt="">
              <img
                  class="left-img"
                  v-else
                  width="25"
                  height="25"
                  :src="require('../../assets/images/quality/task-item.png')" alt="">
            </template>
            <template #right-icon>
              <img
                  v-if="item.bop_planId || item.child.length > 0"
                  class="right-img"
                  width="15"
                  height="15"
                  :src="require('../../assets/images/common/right-arrow.png')" alt="">

              <van-checkbox
                  v-else
                  :name="item"
                  :ref="el => checkboxTaskRefs[index] = el"
                  @click.stop
              />
            </template>
          </van-cell>
        </van-cell-group>
      </van-checkbox-group>

    </van-popup>
    <!--检查任务 end-->

    <!--用户列表 start-->
    <van-popup
        v-model:show="isShowUser"
        closeable
        position="bottom"
        :style="{ height: '90%' }"
        @close="closeUerPop"
    >
      <van-nav-bar
          title="请选择人员"
      />
      <van-checkbox-group @change="changeCheck" v-model="checkerChecked" v-if="selectUserType === 'checkChecker'">
        <van-cell-group inset class="mt-10">
          <van-cell
              v-for="(item, index) in userData"
              clickable
              :key="item.UserId"
              :title="item.RealName"
              @click="toggle(index)"
          >
            <template #right-icon>
              <van-checkbox
                  :name="item.UserId"
                  :ref="el => checkboxRefs[index] = el"
              />
            </template>
          </van-cell>
        </van-cell-group>
      </van-checkbox-group>
      <van-checkbox-group @change="changeCheck" v-model="zgrChecked" v-else-if="selectUserType === 'checkZgr'">
        <van-cell-group inset class="mt-10">
          <van-cell
              v-for="(item, index) in userData"
              clickable
              :key="item.UserId"
              :title="item.RealName"
              @click="toggle(index)"
          >
            <template #right-icon>
              <van-checkbox
                  :name="item.UserId"
                  :ref="el => checkboxRefs[index] = el"
                  @click.stop
              />
            </template>
          </van-cell>
        </van-cell-group>
      </van-checkbox-group>
      <van-checkbox-group @change="changeCheck" v-model="ysrChecked" v-else-if="selectUserType === 'checkYsr'">
        <van-cell-group inset class="mt-10">
          <van-cell
              v-for="(item, index) in userData"
              clickable
              :key="item.UserId"
              :title="item.RealName"
              @click="toggle(index)"
          >
            <template #right-icon>
              <van-checkbox
                  :name="item.UserId"
                  :ref="el => checkboxRefs[index] = el"
                  @click.stop
              />
            </template>
          </van-cell>
        </van-cell-group>
      </van-checkbox-group>

    </van-popup>
    <!--用户列表 end-->

    <!--检查记录 start-->
    <van-popup
        style="background-color: #f2f2f2"
        v-model:show="isShowRecords"
        position="bottom"
        :style="{ height: '90%' }"
        @close="isShowRecords = false"
    >
      <div class="header-container">
        <img
            width="25"
            height="25"
            :src="require('../../assets/images/common/back-arrow.png')"
            @click="isShowRecords = false"
            alt=""/>
        <span>检查记录</span>
        <span> </span>
      </div>
      <van-list>
        <div class="item-wrapper" v-for="(item, index) in recordData" :key="item.RectificationID">
          <van-swipe-cell>
            <div class="top-wrapper">
              <div class="name-bg">
                <span>{{item.RectificationOperator.split('')[item.RectificationOperator.length - 1]}}</span>
              </div>
              <div class="right-wrapper">
                <span>{{appendRecordStr(item, index)}}</span>
              </div>
            </div>
            <div class="record-center-wrapper">
              <span>{{item.RectificationRemark}}</span>
              <ul class="type-list">
                <li v-for="(items,index) in item.Attachments" :key="items.ExamineAttachmentID" @click="showRecordPre(item.Attachments)">
                  <van-image
                      height="80px"
                      width="80px"
                      :src="appendPhotoSrc(items,true)"></van-image>
                </li>
              </ul>
            </div>
            <div class="bottom-wrapper">
              <span>{{item.RectificationCheckDate}}</span>
            </div>
          </van-swipe-cell>
        </div>
      </van-list>
    </van-popup>
    <!--检查记录 end-->

    <!--检查操作 start-->
    <van-popup
        style="background-color: #f2f2f2"
        v-model:show="isShowCheck"
        position="bottom"
        :style="{ height: '100%' }"
        @close="isShowCheck = false"
    >
      <van-sticky>
        <div class="header-container">
          <img
              width="25"
              height="25"
              :src="require('../../assets/images/common/back-arrow.png')"
              @click="isShowCheck = false"
              alt=""/>
          <span>检查</span>
          <span> </span>
        </div>
      </van-sticky>
      <!--检查信息填写 start-->
      <div class="form-container mt-10">
        <van-cell-group inset>
          <van-field
              style="display: none"
              size="large"
              v-model="checkTitle"
              name="pattern"
              type="textarea"
              placeholder="请输入标题"
              rows="2"
              autosize
              :rules="[{ validator: validatorMessage }]"
          />
        </van-cell-group>
        <van-cell-group inset class="mt-10 upload-container">
          <span class="font-weight-500 mt-12 ml-16"> 关联图片</span>
          <van-uploader :after-read="afterDetailsRead" multiple v-show="detailsPhotoData.length === 0" ref="inputImage">
            <div class="select-wrapper">
              <img :src="require('../../assets/images/quality/add-photo.png')" alt="">
              <span class="font-14">添加图片</span>
            </div>
          </van-uploader>
          <div class="selected-wrapper" v-show="detailsPhotoData.length > 0">
            <ul >
              <li v-for="(item, index) in detailsPhotoData" :key="index" class="ml-10 mt-10">
                <div class="item-wrapper">
                  <van-image
                      radius="4"
                      width="70"
                      height="70"
                      :src="appendDetailsPhotoSrc(item)"
                  />
                  <van-image
                      @click="deleteDetailsPhotoItem(index)"
                      width="20"
                      height="20"
                      class="delete-icon"
                      :src="require('../../assets/images/quality/delete-photo-icon.png')"
                  />
                </div>

              </li>
            </ul>
            <div class="add-icon">
              <van-image
                  radius="4"
                  width="30"
                  height="30"
                  :src="require('../../assets/images/quality/add-photo.png')"
                  @click="clickAddPhoto"
              />
            </div>

          </div>
        </van-cell-group>
        <van-cell-group inset class="mt-10 pt-12">
          <div class="mt-16">
            <span class="font-weight-500  ml-16">检查内容</span>
            <van-field
                size="large"
                v-model="checkContent"
                name="pattern"
                type="textarea"
                placeholder="请输入检查内容"
                rows="2"
                autosize
                :rules="[{ validator: validatorMessage }]"
            />
            <div class="mt-16 ml-16 state-wrapper" >
              <span class="font-weight-500">状态</span>
              <ul>
                <li v-for="(item,index) in stateData" :key="item.name" @click="selectState(index)">
                  <span :class="item.selected?'selected-span':'normal-span'">{{item.name}}</span>
                </li>
              </ul>
            </div>

            <div class="mt-16 ml-16 level-wrapper" v-if="!isHege">
              <span class="font-weight-500">严重等级</span>
              <ul>
                <li v-for="(item,index) in levelData" :key="item.name" @click="selectLevel(index)">
                  <span :class="item.selected?'selected-span':'normal-span'">{{item.name}}</span>
                </li>
              </ul>
            </div>
            <van-divider
                :style="{ padding: '0 16px' }"
            />
            <div class="mt-16 ml-16" v-if="!isHege">
              <span class="font-weight-500">人员</span>
              <div class="zgr-wrapper">
                <div class="zgr-label mt-16">
                  <span class="label">整改人</span>
                  <span class="input ml-50" @click="showUserPop('checkZgr',true)">请选择</span>
                  <img  class="mr-16" :src="require('../../assets/images/common/right-arrow.png')" style="width: 18px;height: 18px" alt="">
                </div>
                <ul class="zgr-list">
                  <li v-for="(item,index) in selectedZgr" :key="item.id">
                    <span class="selected-span">{{item.RealName}}</span>
                  </li>
                </ul>
              </div>
              <van-divider
                  :style="{ padding: '0' }"
              />
              <div class="zgr-wrapper">
                <div class="zgr-label mt-16">
                  <span class="label">验收人</span>
                  <span class="input ml-50" @click="showUserPop('checkYsr',true)">请选择(非必须)</span>
                  <img  class="mr-16" :src="require('../../assets/images/common/right-arrow.png')" style="width: 18px;height: 18px" alt="">
                </div>
                <ul class="zgr-list">
                  <li v-for="(item,index) in selectedYsr" :key="item.id">
                    <span class="selected-span">{{item.RealName}}</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </van-cell-group>
        <van-button class="add-btn" type="primary" @click="judeFunc">{{btnStr}}</van-button>
      </div>
      <!--检查信息填写 end-->
    </van-popup>
    <!--检查操作 end-->

    <!--复检 start-->
    <van-popup
        style="background-color: #f2f2f2"
        v-model:show="isShowCheckAgain"
        position="bottom"
        :style="{ height: '100%' }"
        @close="isShowCheckAgain = false"
    >
      <van-sticky>
        <div class="header-container">
          <img
              width="25"
              height="25"
              :src="require('../../assets/images/common/back-arrow.png')"
              @click="isShowCheckAgain = false"
              alt=""/>
          <span>申请复检</span>
          <span> </span>
        </div>
      </van-sticky>
      <!--检查信息填写 start-->
      <div class="form-container mt-10">
        <van-cell-group inset class="mt-10 upload-container">
          <span class="font-weight-500 mt-12 ml-16"> 关联图片</span>
          <van-uploader :after-read="afterDetailsRead" multiple v-show="detailsPhotoData.length === 0" ref="inputImage">
            <div class="select-wrapper">
              <img :src="require('../../assets/images/quality/add-photo.png')" alt="">
              <span class="font-14">添加图片</span>
            </div>
          </van-uploader>
          <div class="selected-wrapper" v-show="detailsPhotoData.length > 0">
            <ul >
              <li v-for="(item, index) in detailsPhotoData" :key="index" class="ml-10 mt-10">
                <div class="item-wrapper">
                  <van-image
                      radius="4"
                      width="70"
                      height="70"
                      :src="appendDetailsPhotoSrc(item)"
                  />
                  <van-image
                      @click="deleteDetailsPhotoItem(index)"
                      width="20"
                      height="20"
                      class="delete-icon"
                      :src="require('../../assets/images/quality/delete-photo-icon.png')"
                  />
                </div>

              </li>
            </ul>
            <div class="add-icon">
              <van-image
                  radius="4"
                  width="30"
                  height="30"
                  :src="require('../../assets/images/quality/add-photo.png')"
                  @click="clickAddPhoto"
              />
            </div>

          </div>
        </van-cell-group>
        <van-cell-group inset class="mt-10 pt-12">
          <div class="mt-16">
            <span class="font-weight-500  ml-16">申请复检内容</span>
            <van-field
                size="large"
                v-model="checkAgianContent"
                name="pattern"
                type="textarea"
                placeholder="请输入申请复检内容"
                rows="2"
                autosize
                :rules="[{ validator: validatorMessage }]"
            />
          </div>
        </van-cell-group>
        <van-button class="add-btn" type="primary" @click="judeFunc">提交</van-button>
      </div>
      <!--检查信息填写 end-->
    </van-popup>
    <!--复检 end-->

    <!--验收 start-->
    <van-popup
        style="background-color: #f2f2f2"
        v-model:show="isShowAccept"
        position="bottom"
        :style="{ height: '100%' }"
        @close="isShowAccept = false"
    >
      <van-sticky>
        <div class="header-container">
          <img
              width="25"
              height="25"
              :src="require('../../assets/images/common/back-arrow.png')"
              @click="isShowAccept = false"
              alt=""/>
          <span>验收</span>
          <span> </span>
        </div>
      </van-sticky>
      <!--检查信息填写 start-->
      <div class="form-container mt-10">
        <van-cell-group inset class="mt-10 upload-container">
          <span class="font-weight-500 mt-12 ml-16"> 关联图片</span>
          <van-uploader :after-read="afterDetailsRead" multiple v-show="detailsPhotoData.length === 0" ref="inputImage">
            <div class="select-wrapper">
              <img :src="require('../../assets/images/quality/add-photo.png')" alt="">
              <span class="font-14">添加图片</span>
            </div>
          </van-uploader>
          <div class="selected-wrapper" v-show="detailsPhotoData.length > 0">
            <ul >
              <li v-for="(item, index) in detailsPhotoData" :key="index" class="ml-10 mt-10">
                <div class="item-wrapper">
                  <van-image
                      radius="4"
                      width="70"
                      height="70"
                      :src="appendDetailsPhotoSrc(item)"
                  />
                  <van-image
                      @click="deleteDetailsPhotoItem(index)"
                      width="20"
                      height="20"
                      class="delete-icon"
                      :src="require('../../assets/images/quality/delete-photo-icon.png')"
                  />
                </div>

              </li>
            </ul>
            <div class="add-icon">
              <van-image
                  radius="4"
                  width="30"
                  height="30"
                  :src="require('../../assets/images/quality/add-photo.png')"
                  @click="clickAddPhoto"
              />
            </div>

          </div>
        </van-cell-group>
        <van-cell-group inset class="mt-10">
          <div class="mt-16">
            <span class="font-weight-500  ml-16">验收内容</span>
            <van-field
                size="large"
                v-model="checkAgianContent"
                name="pattern"
                type="textarea"
                placeholder="请输入内容"
                rows="2"
                autosize
                :rules="[{ validator: validatorMessage }]"
            />
          </div>
        </van-cell-group>
        <van-cell-group inset class="mt-10">
          <div class="mt-16 ml-16 state-wrapper" >
            <span class="font-weight-500">验收情况</span>
            <ul>
              <li v-for="(item,index) in stateYsData" :key="item.name" @click="selectYsState(index)">
                <span :class="item.selected?'selected-span':'normal-span'">{{item.name}}</span>
              </li>
            </ul>
          </div>
        </van-cell-group>
        <van-button class="add-btn" type="primary" @click="judeFunc">{{btnStr}}</van-button>
      </div>
      <!--检查信息填写 end-->
    </van-popup>
    <!--验收 end-->

    <!--检查信息填写 start-->
    <div class="form-container mt-10">
      <van-cell-group inset>
        <van-field
            :disabled="!isCanModify"
            size="large"
            v-model="checkTitle"
            name="pattern"
            type="textarea"
            placeholder="请输入标题"
            rows="2"
            autosize
            :rules="[{ validator: validatorMessage }]"
        />
      </van-cell-group>
      <van-cell-group inset class="mt-10 upload-container">
        <span class="font-weight-500 mt-12 ml-16"> 关联图片</span>
        <van-uploader :after-read="afterRead" multiple v-show="photoData.length === 0 && !examineId" ref="inputImage" :disabled="!isCanModify">
          <div class="select-wrapper">
            <img :src="require('../../assets/images/quality/add-photo.png')" alt="">
            <span class="font-14">添加图片</span>
          </div>
        </van-uploader>
        <div class="selected-wrapper" v-show="photoData.length > 0">
          <ul >
            <li v-for="(item, index) in photoData" :key="index" class="ml-10 mt-10" @click="showImagePre">
              <div class="item-wrapper">
                <van-image
                    radius="4"
                    width="70"
                    height="70"
                    :src="appendPhotoSrc(item , false)"
                />
                <van-image
                    v-if="!examineId"
                    @click="deletePhotoItem(index)"
                    width="20"
                    height="20"
                    class="delete-icon"
                    :src="require('../../assets/images/quality/delete-photo-icon.png')"
                />
              </div>

            </li>
          </ul>
          <div class="add-icon" v-if="!examineId">
            <van-image
                radius="4"
                width="30"
                height="30"
                :src="require('../../assets/images/quality/add-photo.png')"
                @click="clickAddPhoto"
            />
          </div>

        </div>
      </van-cell-group>
      <van-cell-group inset class="mt-10 pt-12">
        <span class="font-weight-500 mt-12 pt-12 ml-16">基本信息</span>
        <van-field
            v-model="checkType"
            is-link
            readonly
            name="datePicker"
            label="检查分类"
            placeholder="点击选择检查分类"
            @click="isShowType = true"
        />
        <van-field
            v-model="checkStartTime"
            is-link
            readonly
            name="datePicker"
            label="开始日期"
            placeholder="点击选择开始日期"
            @click="isShowStartPicker = true"
        />
        <div v-if="isCanModify">
          <van-popup v-model:show="isShowStartPicker" position="bottom">
            <van-date-picker @confirm="onConfirmStartTime" @cancel="isShowStartPicker = false" v-model="currentDate" title="选择日期"/>
          </van-popup>
        </div>

        <van-field
            v-model="checkEndTime"
            is-link
            readonly
            name="datePicker"
            label="结束日期"
            placeholder="点击选择结束日期"
            @click="isShowEndPicker = true"
        />
        <div v-if="isCanModify">
          <van-popup v-model:show="isShowEndPicker" position="bottom">
            <van-date-picker @confirm="onConfirmEndTime" @cancel="isShowEndPicker = false" v-model="currentDate" title="选择日期"/>
          </van-popup>
        </div>

        <van-field
            v-model="checkerName"
            is-link
            readonly
            name="datePicker"
            label="检查人"
            placeholder="点击选择检查人"
            @click="showUserPop('checkChecker',false)"
        />

      </van-cell-group>
      <van-cell-group inset class="mt-10">
        <div v-if="!examineId">
          <van-field name="checkbox" label="发起整改">
            <template #input>
              <van-checkbox class="van-checkbox" v-model="isJlChecked" shape="square" />
            </template>
          </van-field>
        </div>
        <div v-if="isJlChecked" class="mt-16">
          <span class="font-weight-500  ml-16">检查内容</span>
          <van-field
              :disabled="!isCanModify"
              size="large"
              v-model="checkContent"
              name="pattern"
              type="textarea"
              placeholder="请输入检查内容"
              rows="2"
              autosize
              maxlength="50"
              show-word-limit
              :rules="[{ validator: validatorMessage }]"
          />
          <div v-if="examineId">
              <ul class="check-photo-list" v-if="recordData.length > 0">
                <li v-for="(item,index) in recordData[0].Attachments" :key="item.ExamineAttachmentID" @click="showRecordPre(recordData[0].Attachments)">
                  <van-image
                      height="80px"
                      width="80px"
                      :src="appendPhotoSrc(item,true)"></van-image>
                </li>
              </ul>
            <div class="mt-16 ml-16 state-wrapper" v-if="stateData.length > 0">
              <span class="font-weight-500">状态</span>
              <ul>
                <li v-for="(item,index) in stateData" :key="item.name" @click="selectState(index)">
                  <span :class="item.selected?'selected-span':'normal-span'">{{item.name}}</span>
                </li>
              </ul>
            </div>
            <div class="mt-16 ml-16 level-wrapper" v-if="levelData.length > 0">
              <span class="font-weight-500">严重等级</span>
              <ul>
                <li v-for="(item,index) in levelData" :key="item.name" @click="selectLevel(index)">
                  <span :class="item.selected?'selected-span':'normal-span'">{{item.name}}</span>
                </li>
              </ul>
            </div>
          </div>
          <div v-else>
            <!-- <div class="mt-16 ml-16 state-wrapper">
              <span class="font-weight-500">状态</span>
              <ul>
                <li v-for="(item,index) in stateData" :key="item.name" @click="selectState(index)">
                  <span :class="item.selected?'selected-span':'normal-span'">{{item.name}}</span>
                </li>
              </ul>
            </div> -->
            <div class="mt-16 ml-16 level-wrapper" v-if="!isHege">
              <span class="font-weight-500">严重等级</span>
              <ul>
                <li v-for="(item,index) in levelData" :key="item.name" @click="selectLevel(index)">
                  <span :class="item.selected?'selected-span':'normal-span'">{{item.name}}</span>
                </li>
              </ul>
            </div>
          </div>

          <van-divider
              v-if="!isHege"
              :style="{ padding: '0 16px' }"
          />
          <div class="mt-16 ml-16" v-if="!isHege" >
            <span class="font-weight-500">人员</span>
            <div class="zgr-wrapper">
              <div class="zgr-label mt-16">
                <span class="label">整改人</span>
                <span class="input ml-50" @click="showUserPop('checkZgr',false)">{{zgrTip}}</span>
                <img  class="mr-16" :src="require('../../assets/images/common/right-arrow.png')" style="width: 18px;height: 18px" alt="">
              </div>
              <ul class="zgr-list">
                <li v-for="(item,index) in selectedZgr" :key="item.id">
                  <span class="selected-span">{{item.RealName}}</span>
                </li>
              </ul>
            </div>
            <van-divider
                :style="{ padding: '0' }"
            />
            <div class="zgr-wrapper">
              <div class="zgr-label mt-16">
                <span class="label">验收人</span>
                <span class="input ml-50" @click="showUserPop('checkYsr',false)">{{ysrTip}}</span>
                <img  class="mr-16" :src="require('../../assets/images/common/right-arrow.png')" style="width: 18px;height: 18px" alt="">
              </div>
              <ul class="zgr-list">
                <li v-for="(item,index) in selectedYsr" :key="item.id">
                  <span class="selected-span">{{item.RealName}}</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </van-cell-group>
      <van-cell-group inset class="mt-10 pt-12 pb-12">
        <div class="mt-16 ml-16" >
          <span class="font-weight-500">关联内容</span>
          <div class="zgr-wrapper">
            <div class="zgr-label mt-16">
              <span class="label">构件&nbsp;</span>
              <span class="input ml-50" @click="selectElements">{{elementTip}}</span>
              <img  class="mr-16" :src="require('../../assets/images/common/right-arrow.png')" style="width: 18px;height: 18px" alt="">
            </div>
            <ul class="zgr-list">
              <li v-for="(item,index) in selectedEle" :key="item.bc_guid_materialtype">
                <span class="selected-span">{{item.bm_materialname}}</span>
              </li>
            </ul>
          </div>
          <van-divider
              :style="{ padding: '0' }"
          />
          <div class="zgr-wrapper">
            <div class="zgr-label mt-16">
              <span class="label">任务&nbsp;</span>
              <span class="input ml-50" @click="gotoTask">{{taskTip}}</span>
              <img  class="mr-16" :src="require('../../assets/images/common/right-arrow.png')" style="width: 18px;height: 18px" alt="">
            </div>
            <ul class="zgr-list">
              <li v-for="(item,index) in selectedTask" :key="item.UGuid_">
                <span class="selected-span">{{item.NAME_}}</span>
              </li>
            </ul>
          </div>
        </div>
      </van-cell-group>
      <van-button
          :disabled="!isCanCheck && examineId"
          v-if="btnText === '检查' || btnText === '提交'" class="add-btn" :type="isCanCheck? 'primary' : 'warning'" @click="submit">{{examineId ? '检查' : isJlChecked ? btnStr : '提交' }}</van-button>
      <div class="btn-wrapper" v-else>
        <van-image
            :src="require('../../assets/images/quality/records.png')"
            width="20px"
            height="20px"
        />
        <span @click="isShowRecords = true">流转记录</span>
        <van-button class="btn"  color="#f7a500" size="mini" @click="judeUser">{{btnText}}</van-button>
      </div>
    </div>
    <!--检查信息填写 end-->
  </div>
</template>

<script setup>
import router from "@/router";
import {onBeforeUpdate, onMounted, ref, watch} from "vue";
import {getCurrentDate} from "@/utils";
import api from "@/api";
import {getToken} from "@/utils/token";
import {getOrganizeId} from "@/utils/organizeId";
import {showImagePreview, showToast} from "vant";
import {useRoute} from "vue-router";
import {getUserInfo} from "@/utils/user";

/**
 * 左侧点击回退上一级路由
 * 注：replace 方法不会增加路由信息
 */
function onClickLeft(){
  router.back()
}
const checkZgr = ref([])
const checkYsr = ref([])
const checkContent = ref('')
const checkAgianContent = ref('')
const isJlChecked = ref(false)
const checkerChecked = ref([]);
const zgrChecked = ref([]);
const ysrChecked = ref([]);
const eleChecked = ref([]);
const taskChecked = ref([]);
let isShowUser = ref(false)
let isShowType = ref(false)
let currentDate = ref([]);
const userData = ref([
  {
    id: '10',
    name: '张一',
    isSelected: false
  },
  {
    id: '12',
    name: '李二',
    isSelected: false
  },
  {
    id: '31',
    name: '张三',
    isSelected: false
  },
  {
    id: '44',
    name: '李四',
    isSelected: false
  },{
    id: '5',
    name: '张五',
    isSelected: false
  },
  {
    id: '65',
    name: '李六',
    isSelected: false
  }
])
const levelData= ref([
  {
    name:'一般',
    selected: false
  },
  {
    name:'严重',
    selected: false
  },
  {
    name:'非常严重',
    selected: false
  }
])
const levelYsData= ref([
  {
    name:'一般',
    selected: false
  },
  {
    name:'严重',
    selected: false
  },
  {
    name:'非常严重',
    selected: false
  }
])
const stateData= ref([
  {
    name:'合格',
    value: 1,
    selected: false
  },
  {
    name:'不合格',
    value:  0,
    selected: false
  }
])
const stateYsData= ref([
  {
    name:'合格',
    value: 1,
    selected: false
  },
  {
    name:'不合格',
    value:  0,
    selected: false
  }
])
const typeData = ref([])
const selectUserType = ref('')
let checkState = ref('')
let checkYsState = ref('')
let checkLevel = ref('')
let checkCheckerId  = ref('')
const checkboxRefs = ref([]);
const checkboxElementRefs = ref([]);
const checkboxTaskRefs = ref([]);
const checkStartTime = ref('')
const checkEndTime = ref('')
let checkerName = ref('')
let checkType = ref('')
const isSelectedPhoto = ref(false)
const checkTitle = ref('');
const isShowStartPicker = ref(false)
const isShowEndPicker = ref(false)
const isShowTasks = ref(false)
const isShowElements = ref(false)
const isShowRecords = ref(false)
const isShowCheck = ref(false)
const isShowCheckAgain = ref(false)
const isShowAccept = ref(false)
const checkElements = ref('')
/**
 * 手动触发input选取文件点击事件
 * @param type
 */
const inputImage = ref()
function clickAddPhoto () {
  const input = document.getElementsByClassName('van-uploader__input')
  input[0].dispatchEvent(new MouseEvent('click'))
}
/**
 * 选择检查构件
 */
function selectElements() {
  if (!isCanModify.value) return
  isShowElements.value = true
  eleChecked.value = []
  isAllElement.value = true
  getMaterialList(null)

}

/**
 * 关闭构件弹窗
 */
function closeElementsPop(type) {
  switch (type){
    case 'close':
      if (!examineId.value){
        selectedEle.value= []
      }
      break
    case 'confirm':
      if (examineId.value){
        // 修改构件
        modify_flag.value = '4'
        modifyMissionInfo()
      }
      break

  }
  isShowElements.value = false
  isAllElement.value = true
}
/**
 * 关闭任务弹窗
 */
function closeTaskPop(type) {
  switch (type){
    case 'close':
      if (!examineId.value){
        selectedTask.value= []
      }
      break
    case 'confirm':
      // 修改任务
      if (examineId.value){
        modify_flag.value = '5'
        modifyMissionInfo()
      }
      break

  }
  isShowTasks.value = false
}


const elementData = ref([])
const isAllElement = ref(true)
/**
 * 获取构件
 */
async function getMaterialList(baseCode) {
  const res = await api.GetCategories({
    organizeId: getOrganizeId() || projectId.value,
    baseCode,
    Token: getToken()
  })
  if (res.data.Ret === 1 ){
    elementData.value = res.data.Data.list
  }

}
/**
 * 获取子集构件
 */
async function getMaterialItemList(code) {
  isAllElement.value = false
  const res = await api.GetCategoriesItem({
    PageNum: 1,
    PageSize: 500,
    SortField: 'bm_materialcode',
    organizeId: getOrganizeId() || projectId.value,
    bc_guid_materialtype: code,
    token: getToken()
  })
  if (res.data.Ret === 1 ){
    /**
     *    "DirectChildrenCount": 2, // 包含子文件夹
     *    "ChildrenItemCount": 21,// 文件夹下数据
     */
    elementData.value = res.data.Data.Data
  }

}
/**
 * 任务选择
 * @param index
 */
const toggleTask = (index,item) => {
  if (item.bop_guid){
    getPlanItemList(planData.value[index].bop_planId)
  }else {
    if (planData.value[index].child.length > 0){
      planData.value = planData.value[index].child
    }else {
      checkboxTaskRefs.value[index].toggle();
    }
  }
};

/**
 * 递归数据 组装子类数据
 * @param item
 * @param origin
 */
function handleData(item,origin) {
  const tempData = []
  for (const planDatum of origin) {
    if (item.UID_ === planDatum.PARENTTASKUID_) {
      //为子集
      tempData.push(planDatum)
    }
  }
  if (tempData.length > 0){
    console.log('tempData',tempData)
    Object.assign(item,{child: tempData});
    console.log('item',item)
    if (item.child.length > 0){
      for (const tempDatum of item.child) {
        handleData(tempDatum,origin)
      }
    }
    planData.value.push(item)
    console.log('plan',planData.value)
  }else {
    Object.assign(item,{child: []});
  }
}

const planData = ref([])
/**
 * 获取外层任务
 */
async function getPlanList() {
  const res = await api.GetPlanList({
    organizeId: getOrganizeId() || projectId.value,
    Token: getToken()
  })
  if (res.data.Ret === 1 ){
    planData.value = res.data.Data
  }
}
/**
 * 获取子集数据
 */
async function getPlanItemList(code) {
  const res = await api.GetPlanItemList({
    planId: code,
    Token: getToken()
  })
  if (res.data.Ret === 1 ){
    const taskData = res.data.Data.Tasks
    // 递归数据
    if (taskData.length > 0 ){
      planData.value = []
      for (const taskDatum of taskData) {
        handleData(taskDatum,taskData)
      }
    }
  }
}
const selectedEle = ref([])
const selectedTask = ref([])


/**
 * 构件复选框变化监听方法
 * @param val
 */
function eleChangeCheck(val) {
  selectedEle.value = []
  selectedEle.value = val
  // for (const valElement of val) {
  //   let index = elementData.value.findIndex((item) => item.bmc_guid === valElement);
  //   selectedEle.value.push(elementData.value[index])
  // }
  console.log('构件选择',selectedEle.value)
}
/**
 * 任务复选框变化监听方法
 * @param val
 */
function taskChangeCheck(val) {
  selectedTask.value = []
  selectedTask.value = val
  // for (const valElement of val) {
  //   let index = elementData.value.findIndex((item) => item.bmc_guid === valElement);
  //   selectedEle.value.push(elementData.value[index])
  // }
  console.log('任务选择',selectedTask.value)
}
/**
 * 选择检查分类
 * @param item
 */
function confirmElements(item) {
  checkElements.value = item.AedtName
  isShowElements.value = false
}
const selectCheckType = ref('')

const Aedt_guid = ref('')
/**
 * 选择检查分类
 * @param item
 */
function selectType(item) {
  Aedt_guid.value = item.AedtGuid
  checkType.value = item.AedtName
  isShowType.value = false
}

const selectedZgr = ref([])
const selectedYsr = ref([])
/**
 * 复选框变化监听方法
 * @param val
 */
function changeCheck(val) {
  switch (selectUserType.value) {
    case 'checkChecker':
      break
    case 'checkZgr':
      selectedZgr.value = []
      for (const valElement of val) {
        let index = userData.value.findIndex((item) => item.UserId === valElement);
        selectedZgr.value.push(userData.value[index])
      }
      console.log('整改人',selectedZgr.value)
      break
    case 'checkYsr':
      selectedYsr.value = []
      for (const valElement of val) {
        let index = userData.value.findIndex((item) => item.UserId === valElement);
        selectedYsr.value.push(userData.value[index])
      }
      console.log('验收人',selectedYsr.value)
      break
  }
  console.log('人员',val)
}

/**
 * 关闭选择人员方法
 */
function closeUerPop() {
}
/**
 * 显示选择人员弹窗
 */
function showUserPop(type,isCheck) {
  if (isCheck){
    selectUserType.value = type
    isShowUser.value = true
  }else {
    // 新建 编辑
    if (!isCanModify.value) return
    if (type === 'checkChecker'){
      checkerChecked.value= []
      // checkboxRefs.value = [];
    }
    selectUserType.value = type
    isShowUser.value = true
  }
}
function gotoTask() {
  if (!isCanModify.value) return
  isShowTasks.value = true
  taskChecked.value = []
  getPlanList()
}
const isHege = ref(false)
const btnStr =ref('提交')
/**
 * 状态选择方法
 * @param index
 */
function selectState(index) {
  for (let valueElement of stateData.value) {
    valueElement.selected = false
  }
  stateData.value[index].selected = true
  const temp = stateData.value[index]
  checkState.value = temp.value
  if (checkState.value === 1){
    isHege.value = true
    // 合格 隐藏严重等级 整改人 验收人
    btnStr.value = '提交'
  }else {
    isHege.value = false
    btnStr.value = '发起整改'
  }
}
/**
 * 验收状态选择方法
 * @param index
 */
function selectYsState(index) {
  for (let valueElement of stateYsData.value) {
    valueElement.selected = false
  }
  stateYsData.value[index].selected = true
  const temp = stateYsData.value[index]
  checkYsState.value = temp.value
  if (checkYsState.value === 1){
    btnStr.value = '提交'
  }else {
    btnStr.value = '发起整改'
  }
}

/**
 * 严重等级选择
 * @param index
 */
function selectLevel(index) {
  for (let valueElement of levelData.value) {
    valueElement.selected = false
  }
  levelData.value[index].selected = true
  const temp = levelData.value[index]
  checkLevel.value = temp.name
}
/**
 * 构件选择
 * @param index
 */
const toggleElement = (index,item) => {
  if (isAllElement.value){
    if (item.DirectChildrenCount === 0){
      getMaterialItemList(item.bmc_guid)
    }else {
      getMaterialList(item.bmc_code)
    }
  }else {
    checkboxElementRefs.value[index].toggle();
  }
};
/**
 * 人员选择
 * @param index
 */
const toggle = (index) => {
  switch (selectUserType.value) {
    case 'checkChecker':
      checkCheckerId.value = userData.value[index].UserId
      checkerName.value = userData.value[index].Account
      isShowUser.value = false
      break
    case 'checkZgr':
    case 'checkYsr':
      checkboxRefs.value[index].toggle();
      break
  }
};
// 校验函数可以直接返回一段错误提示
const validatorMessage = (val) => `${val} 不合法，请重新输入`;
/**
 * 文件选择后
 * @param file
 */
const photoData = ref([])
const detailsPhotoData = ref([])
const photoFiles = ref([])
const detailsPhotoFiles = ref([])

const afterRead = (file) => {
  const files = []
  if (file instanceof Array){
    for (const temp of file) {
      files.push(temp.file)
      photoFiles.value.push(temp.file)
      photoData.value.push(temp.content)
    }
  }else {
    files.push(file.file)
    photoFiles.value.push(file.file)
    photoData.value.push(file.content)
  }
  console.log('photoData',photoData.value)
  console.log('photoFiles',photoFiles.value)
};
const afterDetailsRead = (file) => {
  const files = []
  if (file instanceof Array){
    for (const temp of file) {
      files.push(temp.file)
      detailsPhotoData.value.push(temp.content)
    }
  }else {
    files.push(file.file)
    detailsPhotoData.value.push(file.content)
  }

  detailsPhotoFiles.value = files
  console.log('photoData',detailsPhotoData.value)
  console.log('photoFiles',detailsPhotoFiles.value)
};

/**
 * 预览图片
 */
function showImagePre() {
  if (examineId.value){
    const temp = []
    // 数组重组
    for (const item of photoData.value) {
      const url = window.IP_CONFIG.BASE_URL + "/" + item.bf_path
      temp.push(url)
    }
    showImagePreview(temp);
  }
}
/**
 * 预览图片
 */
function showRecordPre(items) {
  if (examineId.value){
    const temp = []
    // 数组重组
    for (const item of items) {
      const url = window.IP_CONFIG.BASE_URL + "/" + item.AttachmentUrl
      temp.push(url)
    }
    showImagePreview(temp);
  }
}
/**
 *
 * @param index
 */
function deletePhotoItem(index) {
  photoData.value.splice(index, 1)
}
/**
 *
 * @param index
 */
function deleteDetailsPhotoItem(index) {
  detailsPhotoData.value.splice(index, 1)
}


/**
 * 上传文件
 * @returns {Promise<void>}
 */
async function uploadImages() {
  const fd = new FormData
  for (const file of photoFiles.value) {
    fd.append('Files',file)
  }
  fd.append('Token',getToken())
  const res = await api.UploadImages(fd)
  if (res.data.Ret === 1 ){
    const temp = res.data.Data
    let uploadFileId =''
    for (const tempElement of temp) {
      uploadFileId += tempElement.bf_guid+','
    }
    console.log('ds',uploadFileId)
    if (!examineId.value){
      await addMisson(uploadFileId)
    }else {
      await getDetails()
    }
  }else {
    showToast(res.data.Msg)
  }
}
/**
 * 上传文件
 * @returns {Promise<void>}
 */
async function uploadCheckImages() {
  const fd = new FormData
  for (const file of detailsPhotoFiles.value) {
    fd.append('Files',file)
  }
  fd.append('Token',getToken())
  const res = await api.UploadImages(fd)
  if (res.data.Ret === 1 ){
    const temp = res.data.Data
    let uploadFileId =''
    for (const tempElement of temp) {
      uploadFileId += tempElement.bf_guid+','
    }
    console.log('ds',uploadFileId)
    switch (checkResult.value) {
      case 'A_ToBeCheck':
        await check(uploadFileId)
        break
      case 'B_ToBeRectified':
        await checkAgain(uploadFileId)
        break
      case 'C_ToBeRecheck':
        await accept(uploadFileId)
        break
    }
  }else {
    showToast(res.data.Msg)
  }
}
/**
 * 时间字符串对比
 */
function compareTimeStrings(startStr, endStr) {
  const date1 = new Date(startStr);
  const date2 = new Date(endStr);
  if (date1.getTime() > date2.getTime()) {
    return -1; // startStr is later
  } else if (date1.getTime() < date2.getTime()) {
    return 1; // endStr is later
  } else {
    return 0; // times are equal
  }
}
/**
 * 开始日期确认
 * @param selectedValues
 */
const onConfirmStartTime = ({ selectedValues }) => {
  if(checkEndTime.value) {
    const start = selectedValues.join('-');
    const flag = compareTimeStrings(start, checkEndTime.value)
    if (flag === -1) {
      showToast('开始日期不允许大于结束日期!')
      return
    }
  }
  checkStartTime.value = selectedValues.join('-');
  isShowStartPicker.value = false;
}
/**
 * 结束日期确认
 * @param selectedValues
 */
const onConfirmEndTime = ({ selectedValues }) => {
  if(checkStartTime.value) {
    const end = selectedValues.join('-');
    const flag = compareTimeStrings(checkStartTime.value, end)
    if (flag === -1) {
      showToast('开始日期不允许大于结束日期!')
      return
    }
  }
  checkEndTime.value = selectedValues.join('-');
  isShowEndPicker.value = false;
}
onBeforeUpdate(() => {
});

/**
 * 获取所有人员
 */
async function getUser() {
  const res = await api.GetUserPaged(
      {
        PageNum: 1,
        PageSize: 9999,
        KeyWord: '',
        OrganizeId: getOrganizeId() || projectId.value,
        searchType: 0,
        RoleId: '',
        Token: getToken()
      }
  )
  if (res.data.Ret === 1) {
    userData.value = res.data.Data.list
  }
}

/**
 * 获取检查类型
 * @returns {Promise<void>}
 */
async function getTypes(){
  const res = await api.GetExamTypes(
      {
        organizeId: getOrganizeId() || projectId.value,
        Token: getToken(),
        aedtType :addType.value === '1' ? 'quality' : 'security'
      }
  )
  if (res.data.Ret === 1) {
    typeData.value = res.data.Data.List
  }
}

/**
 * 判断是否有图片上传
 */
function submit() {
  // 新建
  if (!examineId.value){
    // 判断是否勾选发起整改 勾选后需提前判断必填项 以防影响 新增成功 检查失败
    if (isJlChecked.value){
      if (!checkContent.value){
        showToast('必须填写检查内容')
        return
      } else if (!checkLevel.value) {
        showToast('必须选择严重等级')
        return
      } else if (selectedZgr.value.length <= 0) {
        showToast('必须选择整改人')
        return
      }
    }
    if (photoData.value.length > 0){
      uploadImages()
    }else {
      addMisson('')
    }
  }else {
    // 检查
    judeUser()
  }

}
/**
 * 新建质量和安全
 */
async function addMisson(uploadFileId) {
  const res = await api.AddMission({
    LinkType: addType.value,
    Token: getToken(),
    organizeId: getOrganizeId() || projectId.value,
    Aedt_guid: Aedt_guid.value,
    ExamineRemark: checkTitle.value,
    // 开始时间
    ExamineDate: checkStartTime.value,
    // 结束时间
    RectificateDate: checkEndTime.value,
    // 检查人Id
    CheckerUserId: checkCheckerId.value,
    // 构件
    Rel_materialjson: JSON.stringify(selectedEle.value),
    // 任务
    Rel_taskjson: JSON.stringify(selectedTask.value),
    // 图片
    ImageIds: uploadFileId
  })
  if (res.data.Ret === 1){
    if (isJlChecked.value){
      // 防止新增勾选发起整改 新增成功 整改失败 examineId有值后影响UI
      // examineId.value = res.data.Data
      await check(null, res.data.Data)
    }else {
      showToast(res.data.Msg)
      router.back()
    }
  }else {
    showToast(res.data.Msg)
  }
}
function judeFunc() {
    switch (checkResult.value) {
      case 'A_ToBeCheck':
        check('')
        break
      case 'B_ToBeRectified':
        checkAgain('')
        break
      case 'C_ToBeRecheck':
        accept('')
        break
    }

}
/**
 * 确认监理人检查
 * @param id
 * @returns {Promise<void>}
 */
async function check(id, checkExamineId) {
  let zgrStr = ''
  for (const selectedZgrElement of selectedZgr.value) {
    zgrStr += selectedZgrElement.UserId + ','
  }
  let ysStr = ''
  for (const selectedZgrElement of selectedYsr.value) {
    ysStr += selectedZgrElement.UserId + ','
  }
  const fd = new FormData
  for (const file of detailsPhotoFiles.value) {
    fd.append('Files',file)
  }
  fd.append('Token',getToken())
  fd.append('ExamineID',checkExamineId ? checkExamineId : examineId.value)
  fd.append('RectificationRemark',checkContent.value)
  fd.append('IsPassed',checkState.value)
  if (checkState.value === 1){
    // 已经合格
    // 置空数据
  }else {
    fd.append('aede_severitylevel',checkLevel.value)
    fd.append('RelationMemberID',zgrStr)
    fd.append('PrincipalID',ysStr)
  }

  fd.append('OrganizeId',getOrganizeId() || projectId.value)


  const res = await api.CheckMission(fd)
  if (res.data.Ret === 1) {
    isShowCheck.value = false
    showToast(res.data.Msg)
    // await getDetails()
    router.back()
  } else {
    showToast(res.data.Msg)
  }
}

/**
 * 申请复检
 * @param id
 * @returns {Promise<void>}
 */
async function checkAgain(id) {
  const fd = new FormData
  for (const file of detailsPhotoFiles.value) {
    fd.append('Files',file)
  }
  fd.append('Token',getToken())
  fd.append('ExamineID',examineId.value)
  fd.append('OrganizeId',getOrganizeId() || projectId.value)
  fd.append('RectificationRemark',checkAgianContent.value)
  const res = await api.ApplyToAcceptance(fd)
  if (res.data.Ret === 1) {
    isShowCheckAgain.value = false
    showToast(res.data.Msg)
    // await getDetails()
    router.back()
  } else {
    showToast(res.data.Msg)
  }
}
/**
 * 验收
 * @param id
 * @returns {Promise<void>}
 */
async function accept(id) {
  const fd = new FormData
  for (const file of detailsPhotoFiles.value) {
    fd.append('Files',file)
  }
  fd.append('Token',getToken())
  fd.append('ExamineID',examineId.value)
  fd.append('OrganizeId',getOrganizeId() || projectId.value)
  fd.append('RectificationRemark',checkAgianContent.value)
  fd.append('IsPassed',checkYsState.value)
  const res = await api.TryToAcceptance(fd)
  if (res.data.Ret === 1) {
    isShowAccept.value = false
    showToast(res.data.Msg)
    // await getDetails()
    router.back()
  } else {
    showToast(res.data.Msg)
  }
}
const checkResult = ref('')
const btnText = ref('提交')
const recordData = ref([])
const tempData = ref({})
const isCanModify = ref(true)
const isCanCheck = ref(true)
const isCanZg = ref(false)
const isCanYs = ref(false)
const elementTip = ref('请选择(非必须)')
const taskTip = ref('请选择(非必须)')
const zgrTip = ref('请选择')
const ysrTip = ref('请选择(非必须)')

/**
 * 获取详情
 */
async function getDetails() {
  // isJlChecked.value = true
  isCanModify.value = false
  const res = await api.GetMission({
    examineID: examineId.value,
    token: getToken(),
  })
  if (res.data.Ret === 1) {
    const temp = res.data.Data.List
    tempData.value = temp
    tittle.value = temp.ExamineRemark
    isCanCheck.value = temp.User_IsChecker
    isCanZg.value = temp.User_InPrincipal
    isCanYs.value = temp.User_InRelation
    checkTitle.value = temp.ExamineRemark
    checkStartTime.value = temp.ExamineDate.split(' ')[0]
    checkEndTime.value = temp.RectificateDate.split(' ')[0]
    checkerName.value = temp.CheckerName
    checkType.value = temp.aedt_name
    selectedEle.value = temp.Materials
    elementTip.value = '已选择'+selectedEle.value.length +'个构件'
    selectedTask.value = temp.Tasks
    taskTip.value = '已选择'+selectedTask.value.length +'个任务'
    photoData.value = temp.imges
    recordData.value = temp.RecordWithAttachments
    checkCheckerId.value = temp.aede_checkeruserids
    // isJlChecked.value = true
    checkResult.value = temp.ExamineResult
    let btnStr = ''
    switch (temp.ExamineResult) {
      case 'A_ToBeCheck':
        isJlChecked.value = false
        isCanModify.value = createID.value === JSON.parse(getUserInfo()).UserId;
        // console.log('isCanModify',isCanModify.value)
        // console.log(createID.value+JSON.parse(getUserInfo()).UserId)
        btnStr = '检查'
        break
      case 'B_ToBeRectified':
        isJlChecked.value = true
        btnStr = '待整改'
        break
      case 'C_ToBeRecheck':
        isJlChecked.value = true
        btnStr = '待验收'
        break
      case 'D_Qualified':
        isJlChecked.value = true
        btnStr = '已合格'
        break
      case 'E_Closed':
        isJlChecked.value = false
        btnStr = '已关闭'
        break
    }
    btnText.value = btnStr
    const recordTemp = recordData.value[0]
    if (recordTemp) {
      checkContent.value = recordTemp.RectificationRemark

      const tempLevel = levelData.value.find((item) => item.name === temp.aede_severitylevel)
      levelData.value = []
      if (tempLevel){
        tempLevel.selected = true
        levelData.value.push(tempLevel)
      }
      const tempState = stateData.value.find((item) => item.value === recordTemp.RectificationCheckResult)
      stateData.value = []
      if (tempState){
        tempState.selected = true
        stateData.value.push(tempState)
      }
      selectedZgr.value = res.data.Data.List.RelationMember_Users
      zgrTip.value = '已选择'+selectedZgr.value.length +'人'
      selectedYsr.value = res.data.Data.List.Principal_Users
      ysrTip.value = '已选择'+selectedYsr.value.length +'人'
    }

  }else {
    showToast(res.data.Msg)
    router.back()
  }
}

const modify_flag = ref('')
/**
 * 修改详情
 */
async function modifyMissionInfo() {
  const res = await api.ModifyMissionInfo({
    Token: getToken(),
    ExamineID: examineId.value,
    aedt_guid: Aedt_guid.value,
    Title: checkTitle.value,
    organizeId: getOrganizeId() || projectId.value,
    BeginTimeStr: checkStartTime.value,
    EndTimeStr: checkEndTime.value,
    modify_flag: modify_flag.value,
    rel_materialjson: JSON.stringify(selectedEle.value),
    rel_taskjson: JSON.stringify(selectedTask.value)
  })
  if (res.data.Ret ===1 ){
    showToast(res.data.Msg)
    await getDetails()
  }else {
    showToast(res.data.Msg)
    await getDetails()
  }
}

/**
 * 修改检查人
 */
async function modifyMissionChecker() {
  const res = await api.ModifyMissionChecker({
    Token: getToken(),
    ExamineID: examineId.value,
    aedt_guid: Aedt_guid.value,
    CheckerUserId: checkCheckerId.value,
    OrganizeId:getOrganizeId() || projectId.value
  })
  if (res.data.Ret === 1) {
    showToast('操作成功')
    await getDetails()
  } else {
    showToast(res.data.Msg)
  }
}
/**
 * 判断当前登录人权限
 */
function judeUser() {
  switch (checkResult.value) {
    case 'A_ToBeCheck':
      if (tempData.value.User_IsChecker){
        isShowCheck.value = true
        selectState(0)
      }else {
        showToast("您不是检查人，无法检查");
      }
      break
    case "B_ToBeRectified":
      if (tempData.value.User_InRelation){
        isShowCheckAgain.value = true
      }else {
        showToast("您不是整改人，无法申请复检");
      }
      break
    case "C_ToBeRecheck":
      if (tempData.value.User_InPrincipal){
        isShowAccept.value = true
        selectYsState(0)
      }else {
        showToast("您不是验收人，无法验收");

      }

      break

  }

}
/**
 * 获取按钮样式
 */
function getBtnClass() {
  debugger
  if (checkResult.value === 'B_ToBeRectified' ){
    debugger
    if (tempData.value.User_InRelation){
      debugger
      // 是整改人
      return 'primary'
    }else {
      return 'warning'
    }
  }else if (checkResult.value === 'C_ToBeRecheck' ){
    if (tempData.value.User_InPrincipal){
      // 是验收人
      return 'primary'
    }else {
      return 'warning'
    }
  }
}
/**
 * 获取状态
 * @param result
 */
function getStyle(result) {
  let temp = ''
  switch (result) {
    case 'A_ToBeCheck':
      temp ='待检查'
      break
    case 'B_ToBeRectified':
      temp ='待整改'
      break
    case 'C_ToBeRecheck':
      temp ='待验收'
      break
    default:
      statusStyle.value = ''
      break
  }
  return temp
}
/**
 * 获取状态
 * @param result
 */
function getBgStyle(result) {
  let temp = ''
  switch (result) {
    case 'A_ToBeCheck':
      temp = 'status-toBeCheck'
      break
    case 'B_ToBeRectified':
      temp = 'status-toBeRectified'
      break
    case 'C_ToBeRecheck':
      temp = 'status-toBeRecheck'
      break
  }
  return temp
}
/**
 * 整改记录字段拼接
 * @param item
 */
function appendRecordStr(item, index) {
  let temp = ''
  switch (item.RectificationOperateFlag){
    case 1:
      temp = index == recordData.value.length - 1 ? '检查了任务' : '进行了验收'
      break
    case 2:
      temp = '申请复检'
      break
    // case 3:
    //   temp = '验收'
    //   break

  }
  return item.RectificationOperator + temp + '' + '「' + item.aer_counterpart + '」'
}

/**
 * 拼接图片地址
 * @param item
 * @param isRecord
 */
function appendPhotoSrc(item,isRecord) {
  if (examineId.value){
    // 详情图片地址拼接
    if (isRecord){
      return window.IP_CONFIG.BASE_URL + "/" + item.AttachmentUrl
    }else {
      return window.IP_CONFIG.BASE_URL + "/" + item.bf_path
    }
  }else {
    return item
  }
}

/**
 * 拼接图片地址
 * @param item
 */
function appendDetailsPhotoSrc(item) {
  return item
}
const route = useRoute()
const addType = ref(1)
const examineId = ref('')
const tittle = ref('新建')
const createID = ref('')
const projectId  = ref('')
onMounted(()=>{
  addType.value = route.query.type
  examineId.value = route.query.examineId
  createID.value = route.query.createId
  projectId.value = route.query.projectId
  currentDate = getCurrentDate()
  getUser()
  getTypes()
  if (examineId.value){
    getDetails()
  }else {
    tittle.value = addType.value === '1' ? '新建质量巡检' : '新建安全巡检'
  }
})
watch(isJlChecked, async (newValue) =>{
  if (!examineId.value){
    // console.log('jl',newValue)
    if (newValue){
      const user = JSON.parse(getUserInfo())
      checkerName.value = user.RealName
      checkCheckerId.value = user.UserId
      checkState.value = 0
      btnStr.value = '发起整改'
    }else {
      checkerName.value = ''
      checkCheckerId.value = ''
      checkState.value = ''
      btnStr.value = '提交'

    }
  }

})
watch(checkTitle,async (value, oldValue, onCleanup)=>{
  console.log('value1',value)
  console.log('oldValue1',oldValue)
  if (value && examineId.value && oldValue && checkResult.value === 'A_ToBeCheck'){
    modify_flag.value = '1'
    await modifyMissionInfo()
  }
})
watch(checkStartTime,async (value, oldValue, onCleanup)=>{
  console.log('value2',value)
  console.log('oldValue2',oldValue)
  if (value && examineId.value && oldValue && checkResult.value === 'A_ToBeCheck'){
    modify_flag.value = '2'
    await modifyMissionInfo()
  }
})
watch(checkEndTime,async (value, oldValue, onCleanup)=>{
  console.log('value3',value)
  console.log('oldValue3',oldValue)
  if (value && examineId.value && oldValue && checkResult.value === 'A_ToBeCheck'){
    modify_flag.value = '2'
    await modifyMissionInfo()
  }
})
watch(checkType,async (value, oldValue, onCleanup)=>{
  console.log('value4',value)
  console.log('oldValue4',oldValue)
  if (value && examineId.value && oldValue && checkResult.value === 'A_ToBeCheck'){
    modify_flag.value = '3'
    await modifyMissionInfo()
  }
})
watch(checkCheckerId,async (value, oldValue, onCleanup)=>{
  console.log('value5',value)
  console.log('oldValue5',oldValue)
  if (value && examineId.value && oldValue && checkResult.value === 'A_ToBeCheck'){
    await modifyMissionChecker()
  }
})
</script>
<style scoped lang="scss">
.add-container{
  height: 100%;
  background-color: #F4F5F6;
  .form-container{
    background-color: #F4F5F6;
    .title{
      font-weight: 500;
      margin-left: 15px;
    }
    .van-checkbox{
      position: absolute;
      right: 0;
    }
    .upload-container{
      display: flex;
      flex-direction: column;
      padding-bottom: 10px;
      ul{
        display: flex;
        overflow: auto;
        li{
        }
      }
      :deep(.van-uploader__input-wrapper){
        width: 100%;
      }
      .select-wrapper{
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100px;
        img{
          width: 24px;
          height: 24px;
        }
      }
      .selected-wrapper{
        display: flex;
        position: relative;
        ul{
          margin-right: 85px;
        }
        .item-wrapper{
          position: relative;
          .delete-icon{
            position: absolute;
            right: 0;
            top: 0;
          }
        }
        .add-icon{
          border-radius: 4px;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-top: 26px;
          margin-right: 10px;
          width: 70px;
          height: 70px;
          background-color: #E9EBED;
          right: 0;
          position: absolute;
        }
      }
    }
    .level-wrapper{
      ul{
        margin: 20px 0;
        display: flex;
        flex-direction: row;
        li{
          margin-right: 30px;
        }
        .normal-span{
          font-size: 14px;
          color: #4D79FF;
          padding: 8px;
          background-color: #F5F5F5;
        }
        .selected-span{
          font-size: 14px;
          color: white;
          background-color: #007AFF;
          padding: 8px;
        }
      }
    }
    .state-wrapper{
      ul{
        margin: 20px 0;
        display: flex;
        flex-direction: row;
        li{
          margin-right: 30px;
        }
        .normal-span{
          font-size: 14px;
          color: #4D79FF;
          padding: 8px;
          background-color: #F5F5F5;
        }
        .selected-span{
          font-size: 14px;
          color: white;
          background-color: #007AFF;
          padding: 8px;
        }
      }
    }
    .zgr-wrapper{
      padding-bottom: 6px;
      .zgr-label{
        display: flex;
        position: relative;
        span{
          font-size: 14px;
        }
        .label{
        }
        .input{
          color: #c8c9cc;
          padding-right: calc(100% - 200px - 40px);
        }
        img{
          position: absolute;
          right: 0;
        }

      }
      .zgr-list{
        margin-left: 90px;
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
        .selected-span{
          display: block;
          margin: 8px 0 0 8px;
          font-size: 12px;
          color: white;
          background-color: #007AFF;
          padding: 4px;
        }
      }
    }
    .add-btn{
      width: calc(100% - 32px);
      margin: 32px 16px 32px 16px
    }
    .btn-wrapper{
      background-color: white;
      display: flex;
      align-items: center;
      margin-top: 16px;
      padding: 10px;
      span{
        margin-left: 5px;
        font-size: 14px;
        color: #616F7D;
      }
      .btn{
        font-size: 14px;
        padding-left: 15px;
        padding-right: 15px;
        margin-right: 20px;
        position: absolute;
        right: 0;
      }
    }
  }
  .type-list{
    display: flex;
    flex-wrap: wrap;
    margin-top: 30px;
    .selected-span{
      display: block;
      margin: 16px 0 0 16px;
      font-size: 14px;
      color: #616F7D;
      background-color: #F4F5F6;
      padding: 10px;
      &:hover{
        color: white;
        background-color: #3478f6;
      }
    }
  }
  .check-photo-list{
    margin-left: 14px;
    display: flex;
    flex-wrap: wrap;
    margin-top: 10px;
    li{
      margin-right: 10px;
    }
  }
  .header-container{
    display: flex;
    position: relative;
    align-items: center;
    justify-content: center;
    padding-top: 10px;
    padding-bottom: 10px;
    width: 100%;
    background-color: white;
    img{
      position: absolute;
      left: 0;
    }
    span:first-of-type{
      color: black;
    }
    span:last-child{
      position: absolute;
      right: 0;
      margin-right: 10px;
      font-weight: 500;
      color: #007AFF;
    }
  }
  .element-list {
    display: flex;
    flex-direction: column;
    margin-top: 16px;
    li{
      display: flex;
      position: relative;
      margin-bottom: 10px;
      align-items: center;
      .left-img{
        margin: 0 10px 0 10px;
      }
      .right-img{
        position: absolute;
        right: 0;
        margin-right: 10px;
      }
    }

  }
  .item-wrapper{
    display: flex;
    flex-direction: column;
    border-radius: 4px;
    background-color: white;
    margin: 16px;
    .top-wrapper{
      display: flex;
      align-items: center;
      padding: 6px;
      .name-bg{
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #283A4F;
        width: 30px;
        height: 30px;
        border-radius: 360px;
        span{
          font-size: 14px;
          color: white;
        }
      }
      .right-wrapper{
        margin-left: 8px;
        display: flex;
        flex-direction: column;
        font-size: 14px;
        font-weight: 500;
      }
      .status-wrapper{
        font-size: 13px;
        color: white;
        position: absolute;
        right: 0;
        .status-toBeCheck{
          padding: 3px 10px 3px 10px;
          background-color: #646f7c;
          border-radius: 16px 0 0 16px;
        }
        .status-toBeRectified{
          padding: 3px 10px 3px 10px;
          background-color: #a75e21;
          border-radius: 16px 0 0 16px;
        }
        .status-toBeRecheck{
          padding: 3px 10px 3px 10px;
          background-color: #2d69ae;
          border-radius: 16px 0 0 16px;
        }
      }
    }
    .center-wrapper{
      background-color: #F4F5F6;
      display: flex;
      align-items: center;
      padding: 6px;
      margin: 15px 5px 15px 5px;
      span{
        font-weight: 500;
        margin-left: 6px;
        font-size: 14px;
      }
      .type-list{
        display: flex;
        flex-wrap: wrap;
        margin-top: 20px;
      }
    }
    .record-center-wrapper{
      background-color: #F4F5F6;
      display: flex;
      flex-direction: column;
      padding: 6px;
      margin: 15px 5px 15px 5px;
      span{
        font-weight: 500;
        margin-left: 6px;
        font-size: 14px;
      }
      .type-list{
        display: flex;
        flex-wrap: wrap;
        margin-top: 20px;
        li{
          margin-right: 10px;
        }
      }
    }
    .bottom-wrapper{
      margin-top: 15px;
      margin-bottom: 10px;
      span{
        margin-left: 6px;
        font-size: 13px;
        color: #616F7D;
      }
      span:first-of-type{
        color: #A6AEB6;
      }
    }
    .delete-button {
      height: 100%;
    }
  }
  .hide-overflow{
    overflow: hidden;
  }

}

</style>