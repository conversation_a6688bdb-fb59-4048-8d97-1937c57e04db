<template>
    <div id="app">
        <router-view/>
    </div>
</template>

<script setup>
import {useStore} from "vuex";
import {onBeforeMount} from "vue";
import api from "@/api";
const store = useStore()
const code = localStorage.getItem('companyCode') || window.IP_CONFIG.COMPANY_CODE // 默认值

onBeforeMount(() => {
  getCode()
})

/**
 * 企业服务
 */
async function getCode() {
  const res = await api.GetUrlsByCod({
    Code: code
  })
  if (res.data.Ret === 1) {
    // 处理返回数据
    if (res.data.Data.length > 0) {
      store.commit("updateCompanyInfo", res.data.Data)
      // 设置企业服务
      for (const data of res.data.Data) {
        switch (data.cu_urltype) {
          case 'CompanyName':
            store.commit('updateCompanyName', data.cu_url)
            break
          case 'LogUrl':
            store.commit('updateCompanyLogo',data.cu_url)
            break
          case 'MobileTaskApi':
            window.IP_CONFIG.FLOW_URL = data.cu_url
            break
          case 'TaskApi':
            window.IP_CONFIG.TASK_URL = data.cu_url
            break
          case 'newapi':
            window.IP_CONFIG.BASE_URL = data.cu_url
            break
          case 'modelapi':
            window.IP_CONFIG.MODEL_URL = data.cu_url
            break
          case 'newweb':
            window.IP_CONFIG.FRONT_URL = data.cu_url
            break
        }
      }
    }
  }
}
</script>
<style lang="scss">
#app{
  height: 100%;
  background-color: #f2f2f2;
}
</style>
